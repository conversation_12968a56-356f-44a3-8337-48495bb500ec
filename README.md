# 🐺 Werewolf Game

A web-based multiplayer Werewolf (Mafia) game built with Node.js, Express, and Socket.IO. Supports up to 19 players with real-time chat and voting functionality.

## Features

- **Real-time multiplayer gameplay** using WebSockets
- **Support for 3-19 players** with automatic role assignment
- **Complete game phases**: Day discussion, voting, night actions
- **Multiple roles**: <PERSON>wolf, Villager, Seer, Doctor
- **Real-time chat system** for player communication
- **Responsive web interface** that works on desktop and mobile
- **Automatic game flow** with timers and phase transitions

## Game Roles

### 🐺 Werewolf
- **Goal**: Eliminate all villagers
- **Night Action**: Vote to kill a villager
- **Special**: Can communicate with other werewolves

### 👨‍🌾 Villager
- **Goal**: Eliminate all werewolves
- **Day Action**: Participate in voting to eliminate suspected werewolves
- **Special**: None, but forms the majority

### 🔮 Seer
- **Goal**: Help villagers win
- **Night Action**: Investigate one player to learn if they're a werewolf
- **Special**: Receives investigation results privately

### ⚕️ Doctor
- **Goal**: Help villagers win
- **Night Action**: Protect one player from werewolf attacks
- **Special**: Can save players from elimination

## How to Play

### Setup
1. Players join the game lobby by entering their names
2. Once 3+ players have joined, any player can start the game
3. Roles are automatically assigned based on player count

### Game Flow
1. **Day Phase** (2 minutes): All players discuss and share suspicions
2. **Voting Phase** (1 minute): Players vote to eliminate someone
3. **Night Phase** (1 minute): Special roles perform their actions
4. Repeat until one team wins

### Winning Conditions
- **Villagers win**: All werewolves are eliminated
- **Werewolves win**: Werewolves equal or outnumber villagers

## Role Distribution by Player Count

| Players | Werewolves | Villagers | Seer | Doctor |
|---------|------------|-----------|------|--------|
| 3       | 1          | 1         | 1    | 0      |
| 4       | 1          | 2         | 1    | 0      |
| 5       | 1          | 3         | 1    | 0      |
| 6       | 2          | 3         | 1    | 0      |
| 7       | 2          | 4         | 1    | 0      |
| 8       | 2          | 4         | 1    | 1      |
| 9       | 2          | 5         | 1    | 1      |
| 10      | 3          | 5         | 1    | 1      |
| 11-19   | Scales up  | Scales up | 1    | 1      |

## Installation & Running

### Prerequisites
- Node.js (v14 or higher)
- npm

### Quick Start
```bash
# Clone or download the project
cd ww_game

# Install dependencies
npm install

# Start the server
npm start
```

The game will be available at `http://localhost:3000`

### For Development
```bash
# Install dependencies
npm install express socket.io uuid

# Start the server
node server.js
```

## Technical Details

### Architecture
- **Backend**: Node.js with Express and Socket.IO
- **Frontend**: Vanilla HTML, CSS, and JavaScript
- **Real-time Communication**: WebSocket connections via Socket.IO
- **Data Storage**: In-memory (RAM) - no database required

### File Structure
```
ww_game/
├── server.js          # Main server file with game logic
├── package.json       # Project configuration
├── public/
│   ├── index.html     # Main HTML file
│   ├── style.css      # Styling
│   └── script.js      # Client-side JavaScript
└── README.md          # This file
```

### Key Features
- **Automatic role assignment** based on optimal game balance
- **Phase timers** to keep games moving
- **Real-time updates** for all game events
- **Mobile-responsive design**
- **Error handling** for disconnections and invalid actions

## Game Tips

### For New Players
- **Listen carefully** during day discussions
- **Watch voting patterns** to identify suspicious behavior
- **Don't reveal your role** unless absolutely necessary
- **Pay attention to night results** for clues

### Strategy Tips
- **Werewolves**: Blend in during day phase, coordinate kills at night
- **Villagers**: Look for inconsistencies in stories and voting patterns
- **Seer**: Gather information but don't reveal yourself too early
- **Doctor**: Try to protect key players and the Seer if identified

## Troubleshooting

### Common Issues
- **Can't connect**: Make sure the server is running on port 3000
- **Game won't start**: Need at least 3 players to begin
- **Disconnected players**: Game continues with remaining players

### Browser Compatibility
- Chrome, Firefox, Safari, Edge (modern versions)
- Mobile browsers supported
- JavaScript must be enabled

## License

This project is open source and available under the MIT License.

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve the game!
