const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

const app = express();
const server = http.createServer(app);
const io = socketIo(server);

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Game state stored in memory
const gameState = {
    players: new Map(),
    gameStarted: false,
    phase: 'lobby', // lobby, day, night, voting, gameOver
    dayCount: 0,
    votes: new Map(),
    chatMessages: [],
    roles: ['werewolf', 'villager', 'seer', 'doctor'],
    roleAssignments: new Map(),
    eliminatedPlayers: new Set(),
    winner: null,
    nightActions: new Map(), // For seer investigations, doctor protections, werewolf kills
    protectedPlayer: null,
    killedPlayer: null,
    phaseTimer: null,
    votingTimer: null
};

// Werewolf game configuration
const ROLES_CONFIG = {
    3: { werewolf: 1, villager: 1, seer: 1 },
    4: { werewolf: 1, villager: 2, seer: 1 },
    5: { werewolf: 1, villager: 3, seer: 1 },
    6: { werewolf: 2, villager: 3, seer: 1 },
    7: { werewolf: 2, villager: 4, seer: 1 },
    8: { werewolf: 2, villager: 4, seer: 1, doctor: 1 },
    9: { werewolf: 2, villager: 5, seer: 1, doctor: 1 },
    10: { werewolf: 3, villager: 5, seer: 1, doctor: 1 },
    11: { werewolf: 3, villager: 6, seer: 1, doctor: 1 },
    12: { werewolf: 3, villager: 7, seer: 1, doctor: 1 },
    13: { werewolf: 4, villager: 7, seer: 1, doctor: 1 },
    14: { werewolf: 4, villager: 8, seer: 1, doctor: 1 },
    15: { werewolf: 4, villager: 9, seer: 1, doctor: 1 },
    16: { werewolf: 5, villager: 9, seer: 1, doctor: 1 },
    17: { werewolf: 5, villager: 10, seer: 1, doctor: 1 },
    18: { werewolf: 5, villager: 11, seer: 1, doctor: 1 },
    19: { werewolf: 6, villager: 11, seer: 1, doctor: 1 }
};

// Socket.IO connection handling
io.on('connection', (socket) => {
    console.log('New client connected:', socket.id);

    // Handle player joining
    socket.on('joinGame', (playerName) => {
        if (gameState.gameStarted) {
            socket.emit('error', 'Game already started');
            return;
        }

        if (gameState.players.size >= 19) {
            socket.emit('error', 'Game is full');
            return;
        }

        const playerId = uuidv4();
        const player = {
            id: playerId,
            socketId: socket.id,
            name: playerName,
            alive: true,
            role: null
        };

        gameState.players.set(playerId, player);
        socket.playerId = playerId;

        socket.emit('playerJoined', { playerId, playerName });
        io.emit('playersUpdate', Array.from(gameState.players.values()));
        
        console.log(`Player ${playerName} joined with ID ${playerId}`);
    });

    // Handle chat messages
    socket.on('chatMessage', (message) => {
        const player = Array.from(gameState.players.values()).find(p => p.socketId === socket.id);
        if (!player) return;

        const chatMessage = {
            id: uuidv4(),
            playerId: player.id,
            playerName: player.name,
            message: message,
            timestamp: new Date(),
            phase: gameState.phase
        };

        gameState.chatMessages.push(chatMessage);
        io.emit('newChatMessage', chatMessage);
    });

    // Handle game start
    socket.on('startGame', () => {
        if (gameState.gameStarted) return;
        if (gameState.players.size < 3) {
            socket.emit('error', 'Need at least 3 players to start');
            return;
        }

        startGame();
    });

    // Handle voting
    socket.on('vote', (targetPlayerId) => {
        const voter = Array.from(gameState.players.values()).find(p => p.socketId === socket.id);
        if (!voter || !voter.alive || gameState.phase !== 'voting') return;

        gameState.votes.set(voter.id, targetPlayerId);
        io.emit('voteUpdate', {
            voterId: voter.id,
            targetId: targetPlayerId,
            totalVotes: gameState.votes.size,
            alivePlayers: Array.from(gameState.players.values()).filter(p => p.alive).length
        });

        // Check if all alive players have voted
        const alivePlayers = Array.from(gameState.players.values()).filter(p => p.alive);
        if (gameState.votes.size === alivePlayers.length) {
            resolveVoting();
        }
    });

    // Handle night actions
    socket.on('nightAction', (data) => {
        const player = Array.from(gameState.players.values()).find(p => p.socketId === socket.id);
        if (!player || !player.alive || gameState.phase !== 'night') return;

        const { action, targetId } = data;

        if (player.role === 'werewolf' && action === 'kill') {
            gameState.nightActions.set(`kill_${player.id}`, targetId);
        } else if (player.role === 'seer' && action === 'investigate') {
            gameState.nightActions.set(`investigate_${player.id}`, targetId);
        } else if (player.role === 'doctor' && action === 'protect') {
            gameState.nightActions.set(`protect_${player.id}`, targetId);
        }

        checkNightActionsComplete();
    });

    // Handle disconnect
    socket.on('disconnect', () => {
        const player = Array.from(gameState.players.values()).find(p => p.socketId === socket.id);
        if (player) {
            gameState.players.delete(player.id);
            io.emit('playersUpdate', Array.from(gameState.players.values()));
            console.log(`Player ${player.name} disconnected`);
        }
    });
});

// Game logic functions
function startGame() {
    gameState.gameStarted = true;
    gameState.phase = 'day';
    gameState.dayCount = 1;
    
    assignRoles();
    
    // Notify all players about their roles
    gameState.players.forEach((player) => {
        const socket = io.sockets.sockets.get(player.socketId);
        if (socket) {
            socket.emit('roleAssigned', {
                role: player.role,
                gameStarted: true
            });
        }
    });

    io.emit('gameStarted', {
        phase: gameState.phase,
        dayCount: gameState.dayCount
    });

    console.log('Game started with', gameState.players.size, 'players');

    // Start the first day phase after a short delay
    setTimeout(() => {
        startDayPhase();
    }, 3000);
}

function assignRoles() {
    const playerCount = gameState.players.size;
    const roleConfig = ROLES_CONFIG[playerCount];

    const roles = [];
    Object.entries(roleConfig).forEach(([role, count]) => {
        for (let i = 0; i < count; i++) {
            roles.push(role);
        }
    });

    // Shuffle roles
    for (let i = roles.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [roles[i], roles[j]] = [roles[j], roles[i]];
    }

    // Assign roles to players
    const players = Array.from(gameState.players.values());
    players.forEach((player, index) => {
        player.role = roles[index];
        gameState.roleAssignments.set(player.id, roles[index]);
    });
}

function startDayPhase() {
    gameState.phase = 'day';
    gameState.dayCount++;
    gameState.votes.clear();
    gameState.nightActions.clear();

    io.emit('phaseChange', {
        phase: 'day',
        dayCount: gameState.dayCount,
        message: `Day ${gameState.dayCount} begins! Discuss and find the werewolves.`
    });

    // Start day timer (2 minutes)
    gameState.phaseTimer = setTimeout(() => {
        startVotingPhase();
    }, 120000);
}

function startVotingPhase() {
    if (gameState.phaseTimer) {
        clearTimeout(gameState.phaseTimer);
        gameState.phaseTimer = null;
    }

    gameState.phase = 'voting';
    gameState.votes.clear();

    io.emit('phaseChange', {
        phase: 'voting',
        message: 'Voting phase! Choose someone to eliminate.'
    });

    // Start voting timer (1 minute)
    gameState.votingTimer = setTimeout(() => {
        resolveVoting();
    }, 60000);
}

function resolveVoting() {
    if (gameState.votingTimer) {
        clearTimeout(gameState.votingTimer);
        gameState.votingTimer = null;
    }

    // Count votes
    const voteCounts = new Map();
    gameState.votes.forEach((targetId) => {
        voteCounts.set(targetId, (voteCounts.get(targetId) || 0) + 1);
    });

    // Find player with most votes
    let maxVotes = 0;
    let eliminatedPlayerId = null;
    let tiedPlayers = [];

    voteCounts.forEach((count, playerId) => {
        if (count > maxVotes) {
            maxVotes = count;
            eliminatedPlayerId = playerId;
            tiedPlayers = [playerId];
        } else if (count === maxVotes) {
            tiedPlayers.push(playerId);
        }
    });

    // Handle ties (random elimination from tied players)
    if (tiedPlayers.length > 1) {
        eliminatedPlayerId = tiedPlayers[Math.floor(Math.random() * tiedPlayers.length)];
    }

    // Eliminate player
    if (eliminatedPlayerId) {
        const eliminatedPlayer = gameState.players.get(eliminatedPlayerId);
        if (eliminatedPlayer) {
            eliminatedPlayer.alive = false;
            gameState.eliminatedPlayers.add(eliminatedPlayerId);

            io.emit('playerEliminated', {
                playerId: eliminatedPlayerId,
                playerName: eliminatedPlayer.name,
                role: eliminatedPlayer.role,
                votes: maxVotes
            });
        }
    }

    // Check win conditions
    if (checkWinConditions()) {
        return;
    }

    // Start night phase
    setTimeout(() => {
        startNightPhase();
    }, 3000);
}

function startNightPhase() {
    gameState.phase = 'night';
    gameState.nightActions.clear();
    gameState.protectedPlayer = null;
    gameState.killedPlayer = null;

    io.emit('phaseChange', {
        phase: 'night',
        message: 'Night falls... Special roles, make your moves!'
    });

    // Notify special roles
    gameState.players.forEach((player) => {
        if (!player.alive) return;

        const socket = io.sockets.sockets.get(player.socketId);
        if (!socket) return;

        if (player.role === 'werewolf') {
            socket.emit('nightAction', {
                action: 'kill',
                message: 'Choose a player to eliminate',
                targets: Array.from(gameState.players.values())
                    .filter(p => p.alive && p.role !== 'werewolf')
                    .map(p => ({ id: p.id, name: p.name }))
            });
        } else if (player.role === 'seer') {
            socket.emit('nightAction', {
                action: 'investigate',
                message: 'Choose a player to investigate',
                targets: Array.from(gameState.players.values())
                    .filter(p => p.alive && p.id !== player.id)
                    .map(p => ({ id: p.id, name: p.name }))
            });
        } else if (player.role === 'doctor') {
            socket.emit('nightAction', {
                action: 'protect',
                message: 'Choose a player to protect',
                targets: Array.from(gameState.players.values())
                    .filter(p => p.alive)
                    .map(p => ({ id: p.id, name: p.name }))
            });
        }
    });

    // Start night timer (1 minute)
    gameState.phaseTimer = setTimeout(() => {
        resolveNightActions();
    }, 60000);
}

function checkNightActionsComplete() {
    const aliveWerewolves = Array.from(gameState.players.values())
        .filter(p => p.alive && p.role === 'werewolf');
    const aliveSeer = Array.from(gameState.players.values())
        .find(p => p.alive && p.role === 'seer');
    const aliveDoctor = Array.from(gameState.players.values())
        .find(p => p.alive && p.role === 'doctor');

    let expectedActions = 0;
    let completedActions = 0;

    // Count expected werewolf kills (one kill per werewolf team)
    if (aliveWerewolves.length > 0) {
        expectedActions++;
        const werewolfKills = Array.from(gameState.nightActions.keys())
            .filter(key => key.startsWith('kill_'));
        if (werewolfKills.length > 0) completedActions++;
    }

    // Count seer investigation
    if (aliveSeer) {
        expectedActions++;
        if (gameState.nightActions.has(`investigate_${aliveSeer.id}`)) {
            completedActions++;
        }
    }

    // Count doctor protection
    if (aliveDoctor) {
        expectedActions++;
        if (gameState.nightActions.has(`protect_${aliveDoctor.id}`)) {
            completedActions++;
        }
    }

    // If all actions are complete, resolve night
    if (completedActions === expectedActions) {
        resolveNightActions();
    }
}

function resolveNightActions() {
    if (gameState.phaseTimer) {
        clearTimeout(gameState.phaseTimer);
        gameState.phaseTimer = null;
    }

    // Process doctor protection first
    gameState.nightActions.forEach((targetId, actionKey) => {
        if (actionKey.startsWith('protect_')) {
            gameState.protectedPlayer = targetId;
        }
    });

    // Process werewolf kills
    let killTargets = [];
    gameState.nightActions.forEach((targetId, actionKey) => {
        if (actionKey.startsWith('kill_')) {
            killTargets.push(targetId);
        }
    });

    // Determine who gets killed (majority vote among werewolves, or random if tied)
    if (killTargets.length > 0) {
        const killCounts = new Map();
        killTargets.forEach(targetId => {
            killCounts.set(targetId, (killCounts.get(targetId) || 0) + 1);
        });

        let maxKillVotes = 0;
        let killedTargets = [];
        killCounts.forEach((count, playerId) => {
            if (count > maxKillVotes) {
                maxKillVotes = count;
                killedTargets = [playerId];
            } else if (count === maxKillVotes) {
                killedTargets.push(playerId);
            }
        });

        // Random selection if tied
        if (killedTargets.length > 0) {
            gameState.killedPlayer = killedTargets[Math.floor(Math.random() * killedTargets.length)];
        }
    }

    // Check if killed player was protected
    if (gameState.killedPlayer && gameState.killedPlayer === gameState.protectedPlayer) {
        gameState.killedPlayer = null; // Protection successful
    }

    // Process seer investigations
    gameState.nightActions.forEach((targetId, actionKey) => {
        if (actionKey.startsWith('investigate_')) {
            const seerId = actionKey.split('_')[1];
            const seerSocket = io.sockets.sockets.get(
                gameState.players.get(seerId)?.socketId
            );
            const targetPlayer = gameState.players.get(targetId);

            if (seerSocket && targetPlayer) {
                const isWerewolf = targetPlayer.role === 'werewolf';
                seerSocket.emit('seerResult', {
                    targetName: targetPlayer.name,
                    isWerewolf: isWerewolf
                });
            }
        }
    });

    // Apply kill if any
    if (gameState.killedPlayer) {
        const killedPlayer = gameState.players.get(gameState.killedPlayer);
        if (killedPlayer) {
            killedPlayer.alive = false;
            gameState.eliminatedPlayers.add(gameState.killedPlayer);

            io.emit('playerKilled', {
                playerId: gameState.killedPlayer,
                playerName: killedPlayer.name,
                protected: false
            });
        }
    } else if (gameState.protectedPlayer) {
        io.emit('nightResult', {
            message: 'Someone was attacked but protected by the doctor!'
        });
    } else {
        io.emit('nightResult', {
            message: 'The night was quiet...'
        });
    }

    // Check win conditions
    if (checkWinConditions()) {
        return;
    }

    // Start new day
    setTimeout(() => {
        startDayPhase();
    }, 3000);
}

function checkWinConditions() {
    const alivePlayers = Array.from(gameState.players.values()).filter(p => p.alive);
    const aliveWerewolves = alivePlayers.filter(p => p.role === 'werewolf');
    const aliveVillagers = alivePlayers.filter(p => p.role !== 'werewolf');

    let winner = null;
    let message = '';

    if (aliveWerewolves.length === 0) {
        winner = 'villagers';
        message = 'Villagers win! All werewolves have been eliminated.';
    } else if (aliveWerewolves.length >= aliveVillagers.length) {
        winner = 'werewolves';
        message = 'Werewolves win! They equal or outnumber the villagers.';
    }

    if (winner) {
        gameState.phase = 'gameOver';
        gameState.winner = winner;

        // Clear any running timers
        if (gameState.phaseTimer) {
            clearTimeout(gameState.phaseTimer);
            gameState.phaseTimer = null;
        }
        if (gameState.votingTimer) {
            clearTimeout(gameState.votingTimer);
            gameState.votingTimer = null;
        }

        io.emit('gameOver', {
            winner: winner,
            message: message,
            players: Array.from(gameState.players.values()).map(p => ({
                name: p.name,
                role: p.role,
                alive: p.alive
            }))
        });

        return true;
    }

    return false;
}

function resetGame() {
    // Clear timers
    if (gameState.phaseTimer) {
        clearTimeout(gameState.phaseTimer);
        gameState.phaseTimer = null;
    }
    if (gameState.votingTimer) {
        clearTimeout(gameState.votingTimer);
        gameState.votingTimer = null;
    }

    // Reset game state
    gameState.players.clear();
    gameState.gameStarted = false;
    gameState.phase = 'lobby';
    gameState.dayCount = 0;
    gameState.votes.clear();
    gameState.chatMessages = [];
    gameState.roleAssignments.clear();
    gameState.eliminatedPlayers.clear();
    gameState.winner = null;
    gameState.nightActions.clear();
    gameState.protectedPlayer = null;
    gameState.killedPlayer = null;
}

// Start server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    console.log(`Werewolf game server running on port ${PORT}`);
    console.log(`Open http://localhost:${PORT} to play`);
});
