// Initialize Socket.IO connection
const socket = io();

// Game state
let gameState = {
    playerId: null,
    playerName: null,
    players: [],
    role: null,
    phase: 'lobby',
    selectedVote: null,
    gameStarted: false
};

// DOM elements
const loginScreen = document.getElementById('loginScreen');
const gameScreen = document.getElementById('gameScreen');
const gameOverScreen = document.getElementById('gameOverScreen');
const errorModal = document.getElementById('errorModal');

const playerNameInput = document.getElementById('playerNameInput');
const joinGameBtn = document.getElementById('joinGameBtn');
const startGameBtn = document.getElementById('startGameBtn');
const chatInput = document.getElementById('chatInput');
const sendChatBtn = document.getElementById('sendChatBtn');
const submitVoteBtn = document.getElementById('submitVoteBtn');

const playersList = document.getElementById('playersList');
const chatMessages = document.getElementById('chatMessages');
const gamePhase = document.getElementById('gamePhase');
const dayCounter = document.getElementById('dayCounter');
const playerCount = document.getElementById('playerCount');
const roleInfo = document.getElementById('roleInfo');
const votingArea = document.getElementById('votingArea');
const votingOptions = document.getElementById('votingOptions');
const gameStatus = document.getElementById('gameStatus');

// Event listeners
joinGameBtn.addEventListener('click', joinGame);
startGameBtn.addEventListener('click', startGame);
sendChatBtn.addEventListener('click', sendChatMessage);
chatInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') sendChatMessage();
});
submitVoteBtn.addEventListener('click', submitVote);

// Close error modal
document.querySelector('.close').addEventListener('click', () => {
    errorModal.style.display = 'none';
});

// Socket event listeners
socket.on('playerJoined', (data) => {
    gameState.playerId = data.playerId;
    gameState.playerName = data.playerName;
    showScreen('gameScreen');
    startGameBtn.style.display = 'block';
});

socket.on('playersUpdate', (players) => {
    gameState.players = players;
    updatePlayersList();
    updatePlayerCount();
});

socket.on('newChatMessage', (message) => {
    addChatMessage(message);
});

socket.on('roleAssigned', (data) => {
    gameState.role = data.role;
    gameState.gameStarted = data.gameStarted;
    updateRoleInfo();
    startGameBtn.style.display = 'none';
});

socket.on('gameStarted', (data) => {
    gameState.phase = data.phase;
    gameState.gameStarted = true;
    updateGamePhase();
    updateDayCounter(data.dayCount);
});

socket.on('voteUpdate', (data) => {
    updateVotingStatus(data);
});

socket.on('phaseChange', (data) => {
    gameState.phase = data.phase;
    updateGamePhase();
    if (data.dayCount) updateDayCounter(data.dayCount);
    addSystemMessage(data.message);
});

socket.on('playerEliminated', (data) => {
    addSystemMessage(`${data.playerName} (${data.role}) was eliminated with ${data.votes} votes!`);
    updatePlayersList();
});

socket.on('playerKilled', (data) => {
    addSystemMessage(`${data.playerName} was killed during the night!`);
    updatePlayersList();
});

socket.on('nightResult', (data) => {
    addSystemMessage(data.message);
});

socket.on('nightAction', (data) => {
    showNightActionInterface(data);
});

socket.on('seerResult', (data) => {
    const result = data.isWerewolf ? 'is a WEREWOLF!' : 'is NOT a werewolf.';
    addSystemMessage(`🔮 Your investigation reveals: ${data.targetName} ${result}`);
});

socket.on('gameOver', (data) => {
    showGameOver(data);
});

socket.on('error', (message) => {
    showError(message);
});

// Functions
function joinGame() {
    const name = playerNameInput.value.trim();
    if (!name) {
        showError('Please enter your name');
        return;
    }
    socket.emit('joinGame', name);
}

function startGame() {
    socket.emit('startGame');
}

function sendChatMessage() {
    const message = chatInput.value.trim();
    if (!message) return;
    
    socket.emit('chatMessage', message);
    chatInput.value = '';
}

function submitVote() {
    if (!gameState.selectedVote) return;
    socket.emit('vote', gameState.selectedVote);
    submitVoteBtn.disabled = true;
    submitVoteBtn.textContent = 'Vote Submitted';
}

function showScreen(screenId) {
    document.querySelectorAll('.screen').forEach(screen => {
        screen.classList.remove('active');
    });
    document.getElementById(screenId).classList.add('active');
}

function showError(message) {
    document.getElementById('errorMessage').textContent = message;
    errorModal.style.display = 'block';
}

function updatePlayersList() {
    playersList.innerHTML = '';
    gameState.players.forEach(player => {
        const playerDiv = document.createElement('div');
        playerDiv.className = 'player-item';
        if (player.id === gameState.playerId) {
            playerDiv.classList.add('current-player');
        }
        if (!player.alive) {
            playerDiv.classList.add('eliminated');
        }
        
        playerDiv.innerHTML = `
            <span>${player.name}</span>
            <span class="player-status">${player.alive ? '🟢' : '💀'}</span>
        `;
        playersList.appendChild(playerDiv);
    });
}

function updatePlayerCount() {
    playerCount.textContent = `Players: ${gameState.players.length}/19`;
}

function addChatMessage(message) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'chat-message';

    const timestamp = new Date(message.timestamp).toLocaleTimeString();
    messageDiv.innerHTML = `
        <span class="sender">${message.playerName}:</span>
        <span class="message">${message.message}</span>
        <span class="timestamp">${timestamp}</span>
    `;

    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function addSystemMessage(message) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'chat-message system-message';

    const timestamp = new Date().toLocaleTimeString();
    messageDiv.innerHTML = `
        <span class="sender">🎮 System:</span>
        <span class="message">${message}</span>
        <span class="timestamp">${timestamp}</span>
    `;

    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function updateRoleInfo() {
    if (!gameState.role) return;
    
    const roleDescriptions = {
        werewolf: '🐺 You are a Werewolf! Work with other werewolves to eliminate villagers.',
        villager: '👨‍🌾 You are a Villager! Find and eliminate the werewolves.',
        seer: '🔮 You are the Seer! You can investigate one player each night.',
        doctor: '⚕️ You are the Doctor! You can protect one player each night.'
    };
    
    roleInfo.innerHTML = `
        <h4>Your Role</h4>
        <p>${roleDescriptions[gameState.role]}</p>
    `;
}

function updateGamePhase() {
    const phaseNames = {
        lobby: 'Lobby',
        day: 'Day Phase',
        night: 'Night Phase',
        voting: 'Voting Phase',
        gameOver: 'Game Over'
    };
    
    gamePhase.textContent = phaseNames[gameState.phase] || gameState.phase;
    
    // Show/hide voting area based on phase
    if (gameState.phase === 'voting') {
        showVotingArea();
    } else {
        votingArea.style.display = 'none';
    }
}

function updateDayCounter(day) {
    dayCounter.textContent = day ? `Day ${day}` : '';
}

function showVotingArea() {
    votingArea.style.display = 'block';
    votingOptions.innerHTML = '';
    
    // Get alive players (excluding current player)
    const alivePlayers = gameState.players.filter(p => 
        p.alive && p.id !== gameState.playerId
    );
    
    alivePlayers.forEach(player => {
        const button = document.createElement('button');
        button.className = 'vote-option';
        button.textContent = player.name;
        button.addEventListener('click', () => selectVote(player.id, button));
        votingOptions.appendChild(button);
    });
    
    submitVoteBtn.disabled = true;
    submitVoteBtn.textContent = 'Submit Vote';
}

function selectVote(playerId, buttonElement) {
    // Remove previous selection
    document.querySelectorAll('.vote-option').forEach(btn => {
        btn.classList.remove('selected');
    });
    
    // Select new option
    buttonElement.classList.add('selected');
    gameState.selectedVote = playerId;
    submitVoteBtn.disabled = false;
}

function updateVotingStatus(data) {
    gameStatus.innerHTML = `
        <p>Votes: ${data.totalVotes}/${data.alivePlayers}</p>
    `;
}

function showNightActionInterface(data) {
    votingArea.style.display = 'block';
    votingArea.innerHTML = `
        <h4>${data.message}</h4>
        <div id="nightActionOptions"></div>
        <button id="submitNightActionBtn" disabled>Submit Action</button>
    `;

    const nightActionOptions = document.getElementById('nightActionOptions');
    const submitBtn = document.getElementById('submitNightActionBtn');
    let selectedTarget = null;

    data.targets.forEach(target => {
        const button = document.createElement('button');
        button.className = 'vote-option';
        button.textContent = target.name;
        button.addEventListener('click', () => {
            document.querySelectorAll('.vote-option').forEach(btn => {
                btn.classList.remove('selected');
            });
            button.classList.add('selected');
            selectedTarget = target.id;
            submitBtn.disabled = false;
        });
        nightActionOptions.appendChild(button);
    });

    submitBtn.addEventListener('click', () => {
        if (selectedTarget) {
            socket.emit('nightAction', {
                action: data.action,
                targetId: selectedTarget
            });
            submitBtn.disabled = true;
            submitBtn.textContent = 'Action Submitted';
        }
    });
}

function showGameOver(data) {
    showScreen('gameOverScreen');

    const gameResult = document.getElementById('gameResult');
    gameResult.innerHTML = `
        <h2>${data.message}</h2>
        <h3>Final Results:</h3>
        <div class="final-players">
            ${data.players.map(player => `
                <div class="final-player ${player.alive ? 'alive' : 'dead'}">
                    <span class="player-name">${player.name}</span>
                    <span class="player-role">${player.role}</span>
                    <span class="player-status">${player.alive ? '🟢 Alive' : '💀 Dead'}</span>
                </div>
            `).join('')}
        </div>
    `;

    document.getElementById('newGameBtn').addEventListener('click', () => {
        location.reload();
    });
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    showScreen('loginScreen');
});
