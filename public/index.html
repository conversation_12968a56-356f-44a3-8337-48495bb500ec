<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Werewolf Game</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="app">
        <!-- Login Screen -->
        <div id="loginScreen" class="screen active">
            <div class="container">
                <h1>🐺 Werewolf Game</h1>
                <div class="login-form">
                    <input type="text" id="playerNameInput" placeholder="Enter your name" maxlength="20">
                    <button id="joinGameBtn">Join Game</button>
                </div>
            </div>
        </div>

        <!-- Game Screen -->
        <div id="gameScreen" class="screen">
            <div class="container">
                <!-- Header -->
                <div class="game-header">
                    <h1>🐺 Werewolf Game</h1>
                    <div class="game-info">
                        <span id="gamePhase">Lobby</span>
                        <span id="dayCounter"></span>
                        <span id="playerCount">Players: 0/19</span>
                    </div>
                </div>

                <!-- Main Game Area -->
                <div class="game-content">
                    <!-- Left Panel: Players -->
                    <div class="players-panel">
                        <h3>Players</h3>
                        <div id="playersList"></div>
                        <button id="startGameBtn" style="display: none;">Start Game</button>
                    </div>

                    <!-- Center Panel: Chat -->
                    <div class="chat-panel">
                        <h3>Chat</h3>
                        <div id="chatMessages"></div>
                        <div class="chat-input">
                            <input type="text" id="chatInput" placeholder="Type your message...">
                            <button id="sendChatBtn">Send</button>
                        </div>
                    </div>

                    <!-- Right Panel: Game Actions -->
                    <div class="actions-panel">
                        <h3>Actions</h3>
                        <div id="roleInfo"></div>
                        <div id="votingArea" style="display: none;">
                            <h4>Vote to Eliminate</h4>
                            <div id="votingOptions"></div>
                            <button id="submitVoteBtn" disabled>Submit Vote</button>
                        </div>
                        <div id="gameStatus"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Game Over Screen -->
        <div id="gameOverScreen" class="screen">
            <div class="container">
                <h1>Game Over</h1>
                <div id="gameResult"></div>
                <button id="newGameBtn">New Game</button>
            </div>
        </div>
    </div>

    <!-- Error Modal -->
    <div id="errorModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <p id="errorMessage"></p>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script src="script.js"></script>
</body>
</html>
