* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #fff;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Screen Management */
.screen {
    display: none;
    min-height: 100vh;
}

.screen.active {
    display: block;
}

/* Login Screen */
#loginScreen {
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-form {
    background: rgba(255, 255, 255, 0.1);
    padding: 40px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.login-form h1 {
    margin-bottom: 30px;
    font-size: 2.5em;
}

.login-form input {
    width: 100%;
    padding: 15px;
    margin: 10px 0;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
}

.login-form button {
    width: 100%;
    padding: 15px;
    margin: 10px 0;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    background: #ff6b6b;
    color: white;
    cursor: pointer;
    transition: background 0.3s;
}

.login-form button:hover {
    background: #ff5252;
}

/* Game Screen */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.game-info {
    display: flex;
    gap: 20px;
    font-size: 14px;
}

.game-info span {
    background: rgba(255, 255, 255, 0.2);
    padding: 5px 10px;
    border-radius: 15px;
}

.game-content {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: 20px;
    height: calc(100vh - 200px);
}

/* Panels */
.players-panel, .chat-panel, .actions-panel {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 20px;
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
}

.players-panel h3, .chat-panel h3, .actions-panel h3 {
    margin-bottom: 15px;
    color: #ffd700;
}

/* Players List */
#playersList {
    flex: 1;
    overflow-y: auto;
}

.player-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    margin: 5px 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    transition: background 0.3s;
}

.player-item:hover {
    background: rgba(255, 255, 255, 0.2);
}

.player-item.eliminated {
    opacity: 0.5;
    text-decoration: line-through;
}

.player-item.current-player {
    border: 2px solid #ffd700;
}

/* Chat */
#chatMessages {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 15px;
    padding: 10px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    max-height: 400px;
}

.chat-message {
    margin: 8px 0;
    padding: 8px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
}

.chat-message .sender {
    font-weight: bold;
    color: #ffd700;
}

.chat-message .timestamp {
    font-size: 12px;
    color: #ccc;
    float: right;
}

.chat-message.system-message {
    background: rgba(255, 215, 0, 0.2);
    border-left: 3px solid #ffd700;
}

.chat-message.system-message .sender {
    color: #ffd700;
}

.chat-input {
    display: flex;
    gap: 10px;
}

.chat-input input {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
}

.chat-input button {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    background: #4CAF50;
    color: white;
    cursor: pointer;
    transition: background 0.3s;
}

.chat-input button:hover {
    background: #45a049;
}

/* Actions Panel */
#roleInfo {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
}

#votingArea {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
}

#votingOptions {
    margin: 10px 0;
}

.vote-option {
    display: block;
    width: 100%;
    padding: 10px;
    margin: 5px 0;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    cursor: pointer;
    transition: background 0.3s;
}

.vote-option:hover {
    background: rgba(255, 255, 255, 0.3);
}

.vote-option.selected {
    background: #ff6b6b;
}

/* Buttons */
button {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
}

button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#startGameBtn {
    background: #4CAF50;
    color: white;
    width: 100%;
    margin-top: 15px;
}

#startGameBtn:hover:not(:disabled) {
    background: #45a049;
}

#submitVoteBtn {
    background: #ff6b6b;
    color: white;
    width: 100%;
}

#submitVoteBtn:hover:not(:disabled) {
    background: #ff5252;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border-radius: 10px;
    width: 80%;
    max-width: 500px;
    color: #333;
    text-align: center;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #000;
}

/* Game Over Screen */
#gameOverScreen {
    display: flex;
    align-items: center;
    justify-content: center;
}

#gameOverScreen .container {
    background: rgba(255, 255, 255, 0.1);
    padding: 40px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    max-width: 600px;
}

.final-players {
    margin: 20px 0;
    text-align: left;
}

.final-player {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    margin: 5px 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.final-player.dead {
    opacity: 0.6;
}

.player-role {
    font-weight: bold;
    color: #ffd700;
}

#newGameBtn {
    background: #4CAF50;
    color: white;
    padding: 15px 30px;
    font-size: 16px;
    margin-top: 20px;
}

#newGameBtn:hover {
    background: #45a049;
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
        gap: 15px;
    }
    
    .game-header {
        flex-direction: column;
        gap: 10px;
    }
    
    .game-info {
        flex-wrap: wrap;
        justify-content: center;
    }
}
